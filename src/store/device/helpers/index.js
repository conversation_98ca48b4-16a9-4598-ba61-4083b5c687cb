import { defaultsDeep, keyBy } from 'lodash-es'
import { deviceStatus as deviceStatusDict } from '$/dicts/device/index.js'

/**
 * 获取设备名称
 */
export function getDeviceName(device) {
  return device.product ? device.product.split(':')[1] : '未授权设备'
}

/**
 * 获取备注名称
 */
export function getRemark(deviceId) {
  const value = window.appStore.get('device')?.[deviceId]?.remark
  return value
}

/**
 * 获取历史设备列表
 */
export function getHistoryDevices() {
  const devices = window.appStore.get('device') || {}

  const value = Object.values(devices).map(device => ({
    ...device,
  }))

  return value
}

/**
 * 获取当前连接的设备
 */
export async function getCurrentDevices() {
  const devices = await window.adb.getDeviceList() || []

  return devices.map(device => ({
    ...device,
    id: device.id,
    status: device.type,
    name: getDeviceName(device),
    wifi: ([':', '_adb-tls-connect']).some(item => device.id.includes(item)),
    remark: getRemark(device.id),
  }))
}

/**
 * 迁移 scrcpy 配置
 */
class MigrateScrcpyConfig {
  constructor() {
    this.scrcpyConfig = {}
    this.processedDevices = new Set()
  }

  start(oldDeviceId, newDeviceId) {
    this.scrcpyConfig = window.appStore.get('scrcpy') || {}

    // 检查旧设备是否有配置
    if (!this.scrcpyConfig[oldDeviceId]) {
      return false
    }

    if

    this.scrcpyConfig[newDeviceId] = defaultsDeep(this.scrcpyConfig[newDeviceId] || {}, this.scrcpyConfig[oldDeviceId])

    // 保存更新后的配置
    window.appStore.set('scrcpy', this.scrcpyConfig)

    this.processedDevices.add(oldDeviceId)

    console.log(`Migrated scrcpy config from ${oldDeviceId} to ${newDeviceId}`)
  }

  end() {
    this.scrcpyConfig = window.appStore.get('scrcpy') || {}

    this.processedDevices.forEach((deviceId) => {
      delete this.scrcpyConfig[deviceId]
    })

    window.appStore.set('scrcpy', this.scrcpyConfig)
  }
}

const deviceSortModel = deviceStatusDict.reduce((obj, item, index) => {
  obj[item.value] = index
  return obj
}, {})

/**
 * 合并历史和当前设备列表
 */
export function mergeDevices(historyDevices, currentDevices) {
  const migrateScrcpyConfig = new MigrateScrcpyConfig()

  const legacyRule = (historyDevice) => {
    const unmatchFlag = currentDevices.every(device => !device.id.includes(historyDevice.id))
    const serialMatchFlag = currentDevices.some(device => device.serialNo.includes(historyDevice.serialNo))
    return unmatchFlag && serialMatchFlag
  }

  const legacyDevices = historyDevices.filter(legacyRule)

  for (let index = 0; index < legacyDevices.length; index++) {
    const legacyDevice = legacyDevices[index]

    const matchDevices = currentDevices.filter(device => device.serialNo.includes(legacyDevice.serialNo))

    matchDevices.forEach((device) => {
      migrateScrcpyConfig.start(legacyDevice.id, device.id)
    })
  }

  migrateScrcpyConfig.end()

  const historyMap = keyBy(legacyDevices, 'id')
  const currentMap = keyBy(currentDevices, 'id')

  const mergeDevices = Object.values(defaultsDeep(currentMap, historyMap))

  const value = mergeDevices.sort((a, b) => deviceSortModel[a.status] - deviceSortModel[b.status])

  return value
}

/**
 * 保存设备信息到存储
 */
export function saveDevicesToStore(devices) {
  const cleanedDevices = devices
    .filter(device => !['unauthorized'].includes(device.status))
    .map(device => ({
      ...device,
      status: 'offline',
      type: 'offline',
    }))

  window.appStore.set('device', keyBy(cleanedDevices, 'id'))
}
